{"ControlStates": {"BackToAbsences": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", "OnSelect", "PaddingBottom", "PaddingLeft", "Size", "Text", "<PERSON><PERSON><PERSON>", "Y", "ZIndex", "AutoDisableOnSelect", "<PERSON><PERSON><PERSON>", "ContentLanguage", "BorderColor", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Color", "PressedColor", "DisplayMode", "PressedFill", "Font", "Italic", "Underline", "Strikethrough", "Align", "PaddingTop", "PaddingRight", "Visible", "VerticalAlign", "X", "TabIndex", "maximumHeight", "maximumWidth", "minimumHeight", "minimumWidth"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BackToAbsences", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "BtnCancelRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", "OnSelect", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "AutoDisableOnSelect", "<PERSON><PERSON><PERSON>", "ContentLanguage", "BorderColor", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Color", "PressedColor", "DisplayMode", "PressedFill", "Font", "Italic", "Underline", "Strikethrough", "Align", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "VerticalAlign", "TabIndex", "maximumHeight", "maximumWidth", "minimumHeight", "minimumWidth"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BtnCancelRestriction", "OptimizeForDevices": "Off", "ParentIndex": 9, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "BtnSaveRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["DisabledBorderColor", "DisabledColor", "DisabledFill", "Fill", "FontWeight", "Height", "HoverColor", "HoverFill", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "Patch(\n    Calendar_Restriction_Altoona,\n    Defaults(Calendar_Restriction_Altoona),\n    {\n        Events: ComboEvents.Selected,\n        Date: Text(DatePickerRestriction.SelectedDate, \"yyyy-mm-dd\"),\n        TypeofEvent: ComboTypeOfEvent.Selected\n    }\n);\nReset(ComboEvents);\nReset(DatePickerRestriction);\nReset(ComboTypeOfEvent);\nNotify(\"Restriction added successfully!\", NotificationType.Success)\n", "InvariantPropertyName": "OnSelect", "IsLockable": false, "NameMapSourceSchema": "?"}, "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "AutoDisableOnSelect", "<PERSON><PERSON><PERSON>", "ContentLanguage", "BorderColor", "RadiusTopLeft", "RadiusTopRight", "RadiusBottomLeft", "RadiusBottomRight", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Color", "PressedColor", "DisplayMode", "PressedFill", "Font", "Italic", "Underline", "Strikethrough", "Align", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "VerticalAlign", "TabIndex", "maximumHeight", "maximumWidth", "minimumHeight", "minimumWidth"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "BtnSaveRestriction", "OptimizeForDevices": "Off", "ParentIndex": 8, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoDisableOnSelect", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusTopRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "RadiusBottomRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "User"}], "StyleName": "defaultButtonStyle", "Type": "ControlInfo"}, "ComboEvents": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "ChevronBackground", "ChevronFill", "Height", "Items", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "OnNavigate", "NavigateFields", "Template", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "SearchItems", "IsLockable": false, "NameMapSourceSchema": "?"}, "UsePhoneLayout", "MoreItemsButtonColor", "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "SearchFields", "NoSelectionText", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "true", "InvariantPropertyName": "SelectMultiple", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "DefaultSelectedItems", "Reset", "IsSearchable", "SelectionColor", "Chevron<PERSON>idth", "FlyoutMaximumHeight", "ChevronHoverFill", "ChevronDisabledFill", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "SelectionTagFill", "SelectionTagColor", "MultiValueDelimiter", "AccessibleLabel", "<PERSON><PERSON><PERSON>", "ContentLanguage", "Color", "HoverColor", "PressedColor", "DisabledColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "TabIndex", "DisplayMode", "OnSelect", "OnChange", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "ComboEvents", "OptimizeForDevices": "Off", "ParentIndex": 3, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "NavigateFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "NoSelectionText", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SelectMultiple", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DefaultSelectedItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Reset", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "IsSearchable", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "MultiValueDelimiter", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Template", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "UsePhoneLayout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MoreItemsButtonColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Chevron<PERSON>idth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FlyoutMaximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnNavigate", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnChange", "RuleProviderType": "Unknown"}], "StyleName": "defaultComboboxStyle", "Type": "ControlInfo"}, "ComboTypeOfEvent": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "ChevronBackground", "ChevronFill", "Height", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "Choices(Calendar_Restriction_Altoona.TypeofEvent)", "InvariantPropertyName": "Items", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "OnNavigate", "NavigateFields", "Template", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "SearchItems", "IsLockable": false, "NameMapSourceSchema": "?"}, "UsePhoneLayout", "MoreItemsButtonColor", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "[\"Value1\"]", "InvariantPropertyName": "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "IsLockable": false, "NameMapSourceSchema": "?"}, "SearchFields", "NoSelectionText", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "true", "InvariantPropertyName": "SelectMultiple", "IsLockable": false, "NameMapSourceSchema": "?"}, "<PERSON><PERSON><PERSON>", "DefaultSelectedItems", "Reset", "IsSearchable", "SelectionColor", "Chevron<PERSON>idth", "FlyoutMaximumHeight", "ChevronHoverFill", "ChevronDisabledFill", "ChevronHoverBackground", "ChevronDisabledBackground", "SelectionFill", "SelectionTagFill", "SelectionTagColor", "MultiValueDelimiter", "AccessibleLabel", "<PERSON><PERSON><PERSON>", "ContentLanguage", "Color", "HoverColor", "PressedColor", "DisabledColor", "DisabledBorderColor", "HoverBorderColor", "PressedBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Size", "FontWeight", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Visible", "TabIndex", "DisplayMode", "OnSelect", "OnChange", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "ComboTypeOfEvent", "OptimizeForDevices": "Off", "ParentIndex": 7, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Items", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "NavigateFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON>lay<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SearchFields", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "NoSelectionText", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "SelectMultiple", "RuleProviderType": "User"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "DefaultSelectedItems", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Reset", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "IsSearchable", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "MultiValueDelimiter", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "AccessibleLabel", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Template", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "UsePhoneLayout", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "MoreItemsButtonColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Chevron<PERSON>idth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FlyoutMaximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronHoverBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ChevronDisabledBackground", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "SelectionTagColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnNavigate", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnChange", "RuleProviderType": "Unknown"}], "StyleName": "defaultComboboxStyle", "Type": "ControlInfo"}, "DatePickerRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["BorderColor", "Height", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Fill", {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DisabledColor", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DisabledFill", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DisabledBorderColor", "IsLockable": false, "NameMapSourceSchema": "?"}, {"AFDDataSourceName": "", "AutoRuleBindingEnabled": false, "AutoRuleBindingString": "", "InvariantPropertyName": "DisplayMode", "IsLockable": false, "NameMapSourceSchema": "?"}, "BorderThickness"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DatePickerRestriction", "OptimizeForDevices": "Off", "ParentIndex": 5, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "User"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "User"}], "StyleName": "defaultDatePickerStyle", "Type": "ControlInfo"}, "DateRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Fill", "ImagePosition", "Height", "<PERSON><PERSON><PERSON>", "Size", "Orientation", "LoadingSpinner", "LoadingSpinnerColor"], "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "DateRestriction", "OptimizeForDevices": "Off", "ParentIndex": 0, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ImagePosition", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Orientation", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinner", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LoadingSpinnerColor", "RuleProviderType": "Unknown"}], "StyleName": "defaultScreenStyle", "Type": "ControlInfo"}, "LblAddRestriction": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblAddRestriction", "OptimizeForDevices": "Off", "ParentIndex": 1, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblDateLabel": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblDateLabel", "OptimizeForDevices": "Off", "ParentIndex": 4, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblEventsLabel": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblEventsLabel", "OptimizeForDevices": "Off", "ParentIndex": 2, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}, "LblTypeLabel": {"AllowAccessToGlobals": true, "ControlPropertyState": ["Color", "DisabledColor", "FontWeight", "Height", "Size", "Text", "<PERSON><PERSON><PERSON>", "X", "Y", "ZIndex", "Live", "LineHeight", "Overflow", "AutoHeight", "Wrap", "IsErrorMessage", "<PERSON><PERSON><PERSON>", "Role", "ContentLanguage", "PressedColor", "HoverColor", "BorderColor", "DisabledBorderColor", "PressedBorderColor", "HoverBorderColor", "BorderStyle", "BorderThickness", "FocusedBorderColor", "FocusedBorderThickness", "Fill", "DisabledFill", "PressedFill", "HoverFill", "Font", "Italic", "Underline", "Strikethrough", "PaddingTop", "PaddingRight", "PaddingBottom", "PaddingLeft", "Align", "VerticalAlign", "Visible", "DisplayMode", "TabIndex", "OnSelect", "minimumWidth", "minimumHeight", "maximumWidth", "maximumHeight"], "HasDynamicProperties": false, "IsAutoGenerated": false, "IsComponentDefinition": false, "IsDataControl": false, "IsFromScreenLayout": false, "IsGroupControl": false, "IsLocked": false, "LayoutName": "", "MetaDataIDKey": "", "Name": "LblTypeLabel", "OptimizeForDevices": "Off", "ParentIndex": 6, "PersistMetaDataIDKey": false, "Properties": [{"Category": "Data", "PropertyName": "Text", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Live", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "Role", "RuleProviderType": "Unknown"}, {"Category": "Data", "PropertyName": "ContentLanguage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Color", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FontWeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Height", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Size", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "<PERSON><PERSON><PERSON>", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "X", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Y", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "ZIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "LineHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Overflow", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "AutoHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Wrap", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "IsErrorMessage", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderStyle", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "BorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderColor", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "FocusedBorderThickness", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Fill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisabledFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PressedFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "HoverFill", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Font", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Italic", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Underline", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Strikethrough", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingTop", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingRight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingBottom", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "PaddingLeft", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Align", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "VerticalAlign", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "Visible", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "DisplayMode", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "TabIndex", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "minimumHeight", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumWidth", "RuleProviderType": "Unknown"}, {"Category": "Design", "PropertyName": "maximumHeight", "RuleProviderType": "Unknown"}, {"Category": "Behavior", "PropertyName": "OnSelect", "RuleProviderType": "Unknown"}], "StyleName": "defaultLabelStyle", "Type": "ControlInfo"}}, "TopParentName": "DateRestriction"}